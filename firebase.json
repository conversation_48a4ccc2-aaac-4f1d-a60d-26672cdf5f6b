{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "flutter": {"platforms": {"macos": {"default": {"projectId": "bitacora-657e2", "appId": "1:690664140824:ios:0148dee75c5b28793cd36d", "uploadDebugSymbols": false, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "bitacora-657e2", "configurations": {"macos": "1:690664140824:ios:0148dee75c5b28793cd36d", "web": "1:690664140824:web:2728eb10dbc7836a3cd36d"}}}}}}