import 'dart:convert';

class FormStructure {
  final String title;
  final String description;
  final List<FormField> fields;

  FormStructure({
    required this.title,
    required this.description,
    required this.fields,
  });

  factory FormStructure.fromJson(Map<String, dynamic> json) {
    return FormStructure(
      title: json['title'] ?? 'Formulario sin título',
      description: json['description'] ?? '',
      fields: (json['fields'] as List<dynamic>?)
          ?.map((field) => FormField.fromJson(field))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'fields': fields.map((field) => field.toJson()).toList(),
    };
  }

  static FormStructure? tryParseJson(String jsonString) {
    try {
      final json = jsonDecode(jsonString);
      return FormStructure.fromJson(json);
    } catch (e) {
      return null;
    }
  }
}

class FormField {
  final String id;
  final String name;
  final String label;
  final String type;
  final bool required;
  final String? placeholder;
  final List<String>? options;
  final String? validation;

  FormField({
    required this.id,
    required this.name,
    required this.label,
    required this.type,
    this.required = false,
    this.placeholder,
    this.options,
    this.validation,
  });

  factory FormField.fromJson(Map<String, dynamic> json) {
    return FormField(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      label: json['label'] ?? '',
      type: json['type'] ?? 'text',
      required: json['required'] ?? false,
      placeholder: json['placeholder'],
      options: (json['options'] as List<dynamic>?)?.cast<String>(),
      validation: json['validation'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'label': label,
      'type': type,
      'required': required,
      if (placeholder != null) 'placeholder': placeholder,
      if (options != null) 'options': options,
      if (validation != null) 'validation': validation,
    };
  }
}

enum GenerationState {
  idle,
  loading,
  success,
  error,
}

class TemplateResult {
  final String htmlCode;
  final String title;
  final DateTime generatedAt;

  TemplateResult({
    required this.htmlCode,
    required this.title,
    required this.generatedAt,
  });
}