enum GenerationState {
  idle,
  promptInput,
  generatingJson,
  jsonGenerated,
  generatingHtml,
  htmlGenerated,
  error,
}

enum WorkflowStep {
  promptInput,
  jsonGeneration,
  jsonDisplay,
  htmlGeneration,
}

class TemplateResult {
  final String htmlCode;
  final String title;
  final DateTime generatedAt;

  TemplateResult({
    required this.htmlCode,
    required this.title,
    required this.generatedAt,
  });
}
