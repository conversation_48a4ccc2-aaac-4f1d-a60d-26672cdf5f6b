import 'package:bitacora_template_generator/models/form_structure.dart';
import 'package:bitacora_template_generator/services/file_service.dart';
import 'package:flutter/material.dart';

class JsonInputWidget extends StatefulWidget {
  final Function(FormStructure) onJsonLoaded;
  final VoidCallback onClear;

  const JsonInputWidget({
    super.key,
    required this.onJsonLoaded,
    required this.onClear,
  });

  @override
  State<JsonInputWidget> createState() => _JsonInputWidgetState();
}

class _JsonInputWidgetState extends State<JsonInputWidget> with TickerProviderStateMixin {
  final TextEditingController _jsonController = TextEditingController();
  bool _isValidJson = true;
  String? _errorMessage;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _jsonController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _validateAndProcessJson() {
    final jsonText = _jsonController.text.trim();
    
    if (jsonText.isEmpty) {
      setState(() {
        _isValidJson = false;
        _errorMessage = 'Por favor ingrese o cargue un archivo JSON';
      });
      return;
    }

    if (!FileService.isValidJson(jsonText)) {
      setState(() {
        _isValidJson = false;
        _errorMessage = 'JSON inválido. Verifique la sintaxis.';
      });
      return;
    }

    final formStructure = FormStructure.tryParseJson(jsonText);
    if (formStructure == null) {
      setState(() {
        _isValidJson = false;
        _errorMessage = 'JSON válido pero estructura incorrecta para formulario';
      });
      return;
    }

    setState(() {
      _isValidJson = true;
      _errorMessage = null;
    });

    widget.onJsonLoaded(formStructure);
  }

  void _loadSampleJson() {
    final sampleJson = FileService.getSampleJson();
    setState(() {
      _jsonController.text = sampleJson;
      _isValidJson = true;
      _errorMessage = null;
    });
    _validateAndProcessJson();
  }

  Future<void> _pickFile() async {
    try {
      final jsonContent = await FileService.pickJsonFile();
      if (jsonContent != null) {
        setState(() {
          _jsonController.text = FileService.formatJsonString(jsonContent);
          _isValidJson = true;
          _errorMessage = null;
        });
        _validateAndProcessJson();
      }
    } catch (e) {
      setState(() {
        _isValidJson = false;
        _errorMessage = e.toString();
      });
    }
  }

  void _clearJson() {
    setState(() {
      _jsonController.clear();
      _isValidJson = true;
      _errorMessage = null;
    });
    widget.onClear();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.code,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Estructura JSON',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _clearJson,
                  icon: Icon(
                    Icons.clear,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  tooltip: 'Limpiar',
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Action buttons
            Wrap(
              spacing: 12,
              runSpacing: 8,
              children: [
                AnimatedBuilder(
                  animation: _scaleAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _scaleAnimation.value,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          _animationController.forward().then((_) {
                            _animationController.reverse();
                            _pickFile();
                          });
                        },
                        icon: Icon(
                          Icons.upload_file,
                          color: theme.colorScheme.onSecondary,
                        ),
                        label: Text(
                          'Cargar Archivo',
                          style: TextStyle(
                            color: theme.colorScheme.onSecondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.secondary,
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    );
                  },
                ),
                OutlinedButton.icon(
                  onPressed: _loadSampleJson,
                  icon: Icon(
                    Icons.auto_awesome,
                    color: theme.colorScheme.tertiary,
                  ),
                  label: Text(
                    'Ejemplo',
                    style: TextStyle(
                      color: theme.colorScheme.tertiary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: theme.colorScheme.tertiary),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // JSON Text Field
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _isValidJson 
                      ? theme.colorScheme.outline.withValues(alpha: 0.3)
                      : theme.colorScheme.error,
                  width: _isValidJson ? 1 : 2,
                ),
                color: theme.colorScheme.surface,
              ),
              child: TextField(
                controller: _jsonController,
                maxLines: 12,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontFamily: 'monospace',
                  color: theme.colorScheme.onSurface,
                ),
                decoration: InputDecoration(
                  hintText: 'Pegue aquí su JSON o use los botones de arriba...',
                  hintStyle: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontStyle: FontStyle.italic,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                ),
                onChanged: (value) {
                  if (_errorMessage != null) {
                    setState(() {
                      _errorMessage = null;
                      _isValidJson = true;
                    });
                  }
                },
              ),
            ),
            
            if (_errorMessage != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: theme.colorScheme.onErrorContainer,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onErrorContainer,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            const SizedBox(height: 20),
            
            // Validate button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _validateAndProcessJson,
                icon: Icon(
                  Icons.check_circle,
                  color: theme.colorScheme.onPrimary,
                ),
                label: Text(
                  'Validar JSON',
                  style: TextStyle(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}