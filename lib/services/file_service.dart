import 'dart:convert';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/services.dart';

class FileService {
  static Future<String?> pickJsonFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final contents = await file.readAsString();
        
        // Validate JSON
        jsonDecode(contents);
        return contents;
      }
      return null;
    } catch (e) {
      throw Exception('Error al leer el archivo JSON: \${e.toString()}');
    }
  }

  static Future<bool> saveHtmlFile(String htmlContent, String fileName) async {
    try {
      String? outputFile = await FilePicker.platform.saveFile(
        dialogTitle: 'Guardar plantilla HTML',
        fileName: '\${fileName}.html',
        type: FileType.custom,
        allowedExtensions: ['html'],
      );

      if (outputFile != null) {
        final file = File(outputFile);
        await file.writeAsString(htmlContent);
        return true;
      }
      return false;
    } catch (e) {
      throw Exception('Error al guardar el archivo HTML: \${e.toString()}');
    }
  }

  static Future<void> copyToClipboard(String text) async {
    await Clipboard.setData(ClipboardData(text: text));
  }

  static String formatJsonString(String jsonString) {
    try {
      final dynamic jsonObject = jsonDecode(jsonString);
      const JsonEncoder encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(jsonObject);
    } catch (e) {
      return jsonString;
    }
  }

  static bool isValidJson(String jsonString) {
    try {
      jsonDecode(jsonString);
      return true;
    } catch (e) {
      return false;
    }
  }

  static String getSampleJson() {
    return '''{
  "title": "Formulario de Registro de Cliente",
  "description": "Formulario para capturar información básica del cliente",
  "fields": [
    {
      "id": "nombre",
      "name": "nombre",
      "label": "Nombre Completo",
      "type": "text",
      "required": true,
      "placeholder": "Ingrese nombre completo"
    },
    {
      "id": "email",
      "name": "email", 
      "label": "Correo Electrónico",
      "type": "email",
      "required": true,
      "placeholder": "<EMAIL>"
    },
    {
      "id": "telefono",
      "name": "telefono",
      "label": "Teléfono",
      "type": "tel",
      "required": false,
      "placeholder": "+1234567890"
    },
    {
      "id": "edad",
      "name": "edad",
      "label": "Edad",
      "type": "number",
      "required": true,
      "validation": "min:18,max:99"
    },
    {
      "id": "genero",
      "name": "genero",
      "label": "Género",
      "type": "select",
      "required": false,
      "options": ["Masculino", "Femenino", "Otro", "Prefiero no decir"]
    },
    {
      "id": "intereses",
      "name": "intereses",
      "label": "Intereses",
      "type": "checkbox",
      "required": false,
      "options": ["Tecnología", "Deportes", "Arte", "Música", "Viajes"]
    },
    {
      "id": "comentarios",
      "name": "comentarios",
      "label": "Comentarios Adicionales",
      "type": "textarea",
      "required": false,
      "placeholder": "Ingrese cualquier comentario adicional..."
    }
  ]
}''';
  }
}