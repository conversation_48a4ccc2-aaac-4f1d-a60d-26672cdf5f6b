import 'dart:developer';
import 'dart:io';
import 'package:firebase_ai/firebase_ai.dart';

class AiService {
  static late final GenerativeModel _model;

  static void initialize() {
    // Initialize using Firebase AI with Vertex AI backend
    final vertexInstance = FirebaseAI.vertexAI();
    _model = vertexInstance.generativeModel(model: 'gemini-2.5-flash');
  }

  static Future<String?> generateJsonFromPrompt(
    String prompt,
    File? attachedFile,
  ) async {
    try {
      // Build prompt for JSON generation
      final aiPrompt = await _buildJsonSystemPrompt(prompt);
      final response = await _model.generateContent(aiPrompt);

      if (response.text != null && response.text!.isNotEmpty) {
        // Extract JSON from response
        final jsonString = _extractJsonFromResponse(response.text!);

        return jsonString;
      }

      // Fallback to a basic structure if AI fails
      return null;
    } catch (e) {
      log('Error generating JSON with Firebase AI: $e');
      return null;
    }
  }

  static Future<List<Content>> _buildJsonSystemPrompt(
    String userPrompt,
  ) async {
    return [
      Content.text(
          '''Eres un generador experto de plantillas JSON para formularios. Tu salida SIEMPRE debe ser un único JSON válido y bien formado, siguiendo estrictamente la estructura definida abajo. No incluyas explicaciones, comentarios ni texto fuera del JSON.

Estructura obligatoria:

1. Plantilla:
   - name (string, deducido del caso de uso)
   - organization_id (entero, si no se da usar 1)
   - active (booleano, true por defecto)
   - version (entero, iniciar en 1)
   - previous_version_id (null por defecto)
   - groups (array)

2. Group:
   - template_id (entero, enlazado a la plantilla)
   - name (string, deducido de la temática)
   - order (entero, secuencia lógica)
   - created_at, updated_at (ISO 8601 UTC actual)
   - blocks (array)
   - conditions ([] por defecto)

3. Block:
   - content (string, "" por defecto)
   - row (entero, disposición vertical)
   - order (entero, orden horizontal dentro de la fila)
   - weight (entero, ancho relativo, 1 por defecto)
   - role (null por defecto)
   - custom_field (objeto)
   - custom_field_option (objeto)
   - conditions ([] por defecto)
   - created_at, updated_at (ISO 8601 UTC)

4. custom_field:
   - custom_field_type (uno de: TEXT, TEXT_AREA, INTEGER, FLOAT, DATE_TIME, DATE, TIME, RADIO, SELECT, CHECKBOX, PROJECT, USER, CHECKBOX_GROUP)
   - identifier (string único tipo "field.<id_unico>")
   - name (string, deducido del caso de uso)
   - organization_id (entero)
   - parent_id (null por defecto)
   - allowed_values (array vacío o con opciones si SELECT, RADIO o CHECKBOX_GROUP)
   - created_at, updated_at (ISO 8601 UTC)
   - discarded_at (null por defecto)

5. custom_field_option:
   - custom_field_id (entero)
   - is_required (booleano, deducido de si es obligatorio)
   - placeholder (string, "" por defecto)
   - created_at, updated_at (ISO 8601 UTC)

6. Reglas:
   - Interpretar libremente la descripción del usuario y deducir grupos y campos.
   - Ordenar el formulario de manera lógica.
   - Asignar tipos de campo correctos según el dato descrito.
   - Usar timestamps actuales en UTC.
   - Si el usuario no menciona un dato, no inventarlo salvo que sea necesario para cumplir la estructura.
   - La respuesta debe contener SOLO el JSON, sin explicaciones.

Input del usuario: Descripción libre de su caso de uso, por ejemplo:
"Quiero un formulario para registrar incidentes en obra, con datos del trabajador, fecha, hora, descripción y gravedad."

Output esperado: JSON válido y bien formado según la estructura.'''),
      Content.text(userPrompt)
    ];
  }

  static Future<String> generateHtmlTemplate(String formStructure) async {
    try {
      // Generate HTML template using Firebase AI (Gemini)
      final prompt = _buildPrompt(formStructure);
      final response = await _model.generateContent([Content.text(prompt)]);

      if (response.text != null && response.text!.isNotEmpty) {
        // Clean the response to extract only the HTML code
        String htmlCode = _extractHtmlFromResponse(response.text!);
        return htmlCode.isNotEmpty
            ? htmlCode
            : _getFallbackTemplate(formStructure);
      }

      return _getFallbackTemplate(formStructure);
    } catch (e) {
      // Fallback to mock generation if Firebase AI is not available
      return _getMockHtmlTemplate(formStructure);
    }
  }

  static String _extractJsonFromResponse(String response) {
    // Extract JSON from AI response
    // Look for JSON content between ```json and ``` or { and }

    // First try to find JSON block in markdown format
    final jsonBlockRegex =
        RegExp(r'```json\s*([\s\S]*?)\s*```', caseSensitive: false);
    final match = jsonBlockRegex.firstMatch(response);

    if (match != null && match.group(1) != null) {
      return match.group(1)!.trim();
    }

    // If no markdown block, look for JSON object
    final jsonObjectRegex = RegExp(r'\{[\s\S]*\}');
    final objMatch = jsonObjectRegex.firstMatch(response);

    if (objMatch != null) {
      return objMatch.group(0)!.trim();
    }

    return '';
  }

  static String _extractHtmlFromResponse(String response) {
    // Extract HTML code from Gemini response
    // Look for HTML content between ```html and ``` or <!DOCTYPE html> and </html>

    // First try to find HTML block in markdown format
    final htmlBlockRegex =
        RegExp(r'```html\s*([\s\S]*?)\s*```', caseSensitive: false);
    final match = htmlBlockRegex.firstMatch(response);

    if (match != null && match.group(1) != null) {
      return match.group(1)!.trim();
    }

    // If no markdown block, look for HTML document
    final htmlDocRegex =
        RegExp(r'<!DOCTYPE html[\s\S]*?</html>', caseSensitive: false);
    final docMatch = htmlDocRegex.firstMatch(response);

    if (docMatch != null) {
      return docMatch.group(0)!.trim();
    }

    // If still nothing, return the response as is (might be plain HTML)
    if (response.toLowerCase().contains('<html') &&
        response.toLowerCase().contains('</html>')) {
      return response.trim();
    }

    return '';
  }

  static String _buildPrompt(String formStructure) {
    return '''

''';
  }

  static String _getFallbackTemplate(String formStructure) {
    return '''
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reporte</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.6; color: #333; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; text-align: center; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; font-weight: 700; }
        .header p { font-size: 1.1rem; opacity: 0.9; }
        .report-meta { background: white; padding: 20px; border-radius: 8px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .table-container { background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        table { width: 100%; border-collapse: collapse; }
        th { background: #6366f1; color: white; padding: 15px; text-align: left; font-weight: 600; }
        td { padding: 15px; border-bottom: 1px solid #e5e7eb; }
        tr:hover { background: #f8fafc; }
        .footer { text-align: center; margin-top: 40px; padding: 20px; color: #6b7280; }
        @media (max-width: 768px) {
            .container { padding: 10px; }
            .header h1 { font-size: 2rem; }
            th, td { padding: 10px 8px; font-size: 14px; }
        }
        @media print {
            body { background: white; }
            .container { max-width: none; }
            .header { background: #6366f1 !important; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>title</h1>
            <p>description</p>
        </div>
        
        <div class="report-meta">
            <p><strong>Fecha de generación:</strong> <span id="currentDate"></span></p>
            <p><strong>Total de registros:</strong> <span id="recordCount">0</span></p>
        </div>
        
   
        
        <div class="footer">
            <p>Reporte generado automáticamente • Bitacora AI</p>
        </div>
    </div>
    
    <script>
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('es-ES', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        // Función para agregar datos dinámicamente
      
        function updateRecordCount() {
            const count = document.querySelectorAll('#dataRows tr').length;
            document.getElementById('recordCount').textContent = count;
        }
        
        // Ejemplo de uso:
       
    </script>
</body>
</html>''';
  }

  static String _getMockHtmlTemplate(String formStructure) {
    // Return the same fallback template for now
    // In a real implementation, this could be a different mock template
    return _getFallbackTemplate(formStructure);
  }
}
