import 'package:bitacora_template_generator/models/form_structure.dart';
import 'package:firebase_ai/firebase_ai.dart';

class AiService {
  static late final GenerativeModel _model;

  static void initialize() {
    // Initialize using Firebase AI with Vertex AI backend
    final vertexInstance = FirebaseAI.vertexAI();
    _model = vertexInstance.generativeModel(model: 'gemini-1.5-flash');
  }

  static Future<String> generateHtmlTemplate(
      FormStructure formStructure) async {
    try {
      // Generate HTML template using Firebase AI (Gemini)
      final prompt = _buildPrompt(formStructure);
      final response = await _model.generateContent([Content.text(prompt)]);

      if (response.text != null && response.text!.isNotEmpty) {
        // Clean the response to extract only the HTML code
        String htmlCode = _extractHtmlFromResponse(response.text!);
        return htmlCode.isNotEmpty
            ? htmlCode
            : _getFallbackTemplate(formStructure);
      }

      return _getFallbackTemplate(formStructure);
    } catch (e) {
      // Fallback to mock generation if Firebase AI is not available
      print('Error generating HTML with Firebase AI: $e');
      return _getMockHtmlTemplate(formStructure);
    }
  }

  static String _extractHtmlFromResponse(String response) {
    // Extract HTML code from Gemini response
    // Look for HTML content between ```html and ``` or <!DOCTYPE html> and </html>

    // First try to find HTML block in markdown format
    final htmlBlockRegex =
        RegExp(r'```html\s*([\s\S]*?)\s*```', caseSensitive: false);
    final match = htmlBlockRegex.firstMatch(response);

    if (match != null && match.group(1) != null) {
      return match.group(1)!.trim();
    }

    // If no markdown block, look for HTML document
    final htmlDocRegex =
        RegExp(r'<!DOCTYPE html[\s\S]*?</html>', caseSensitive: false);
    final docMatch = htmlDocRegex.firstMatch(response);

    if (docMatch != null) {
      return docMatch.group(0)!.trim();
    }

    // If still nothing, return the response as is (might be plain HTML)
    if (response.toLowerCase().contains('<html') &&
        response.toLowerCase().contains('</html>')) {
      return response.trim();
    }

    return '';
  }

  static String _buildPrompt(FormStructure formStructure) {
    return '''
Genera ÚNICAMENTE el código HTML completo (sin explicaciones adicionales) para una plantilla HTML profesional y moderna para reportes basada en la siguiente estructura de formulario:

Título: ${formStructure.title}
Descripción: ${formStructure.description}

Campos del formulario:
${formStructure.fields.map((field) => '- ${field.label} (${field.type})${field.required ? ' *requerido*' : ''}').join('\n')}

Requisitos:
1. Crear una plantilla HTML completa con CSS moderno y responsive
2. Incluir una tabla estilizada para mostrar los datos del reporte
3. Agregar encabezado con título y fecha de generación
4. Usar colores profesionales (azul, verde, gris)
5. Incluir estilos para impresión
6. Hacer la tabla responsive para diferentes pantallas
7. Agregar pie de página con información de la empresa

La plantilla debe poder mostrar múltiples registros de datos y ser fácil de leer e imprimir.

IMPORTANTE: 
- Responde ÚNICAMENTE con el código HTML completo
- No incluyas explicaciones antes o después del código
- Usa CSS embedded en el <style> del HTML
- Incluye JavaScript para funcionalidad dinámica si es necesario
- La respuesta debe empezar con <!DOCTYPE html> y terminar con </html>
''';
  }

  static String _getFallbackTemplate(FormStructure formStructure) {
    return '''
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reporte - ${formStructure.title}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.6; color: #333; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; text-align: center; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; font-weight: 700; }
        .header p { font-size: 1.1rem; opacity: 0.9; }
        .report-meta { background: white; padding: 20px; border-radius: 8px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .table-container { background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        table { width: 100%; border-collapse: collapse; }
        th { background: #6366f1; color: white; padding: 15px; text-align: left; font-weight: 600; }
        td { padding: 15px; border-bottom: 1px solid #e5e7eb; }
        tr:hover { background: #f8fafc; }
        .footer { text-align: center; margin-top: 40px; padding: 20px; color: #6b7280; }
        @media (max-width: 768px) {
            .container { padding: 10px; }
            .header h1 { font-size: 2rem; }
            th, td { padding: 10px 8px; font-size: 14px; }
        }
        @media print {
            body { background: white; }
            .container { max-width: none; }
            .header { background: #6366f1 !important; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${formStructure.title}</h1>
            <p>${formStructure.description}</p>
        </div>
        
        <div class="report-meta">
            <p><strong>Fecha de generación:</strong> <span id="currentDate"></span></p>
            <p><strong>Total de registros:</strong> <span id="recordCount">0</span></p>
        </div>
        
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        ${formStructure.fields.map((field) => '<th>${field.label}</th>').join('\n                        ')}
                    </tr>
                </thead>
                <tbody id="dataRows">
                    <!-- Los datos se insertarán aquí dinámicamente -->
                    <tr>
                        ${formStructure.fields.map((field) => '<td>Ejemplo ${field.label}</td>').join('\n                        ')}
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="footer">
            <p>Reporte generado automáticamente • Bitacora AI</p>
        </div>
    </div>
    
    <script>
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('es-ES', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        // Función para agregar datos dinámicamente
        function addDataRow(data) {
            const tbody = document.getElementById('dataRows');
            const row = document.createElement('tr');
            ${formStructure.fields.map((field) => '''
            const ${field.name}Cell = document.createElement('td');
            ${field.name}Cell.textContent = data.${field.name} || '-';
            row.appendChild(${field.name}Cell);''').join('\n            ')}
            tbody.appendChild(row);
            updateRecordCount();
        }
        
        function updateRecordCount() {
            const count = document.querySelectorAll('#dataRows tr').length;
            document.getElementById('recordCount').textContent = count;
        }
        
        // Ejemplo de uso:
        // addDataRow({${formStructure.fields.map((field) => '"${field.name}": "Valor ejemplo"').join(', ')}});
    </script>
</body>
</html>''';
  }

  static String _getMockHtmlTemplate(FormStructure formStructure) {
    // Return the same fallback template for now
    // In a real implementation, this could be a different mock template
    return _getFallbackTemplate(formStructure);
  }
}
