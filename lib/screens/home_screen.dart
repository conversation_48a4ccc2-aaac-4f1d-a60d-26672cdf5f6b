import 'package:bitacora_template_generator/models/form_structure.dart';
import 'package:bitacora_template_generator/services/ai_service.dart';
import 'package:bitacora_template_generator/widgets/html_viewer_widget.dart';
import 'package:bitacora_template_generator/widgets/json_input_widget.dart';
import 'package:flutter/material.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  FormStructure? _currentFormStructure;
  String? _generatedHtml;
  GenerationState _state = GenerationState.idle;
  String? _errorMessage;
  late AnimationController _buttonAnimationController;
  late Animation<double> _buttonScaleAnimation;

  @override
  void initState() {
    super.initState();
    _buttonAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _buttonScaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(
          parent: _buttonAnimationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _buttonAnimationController.dispose();
    super.dispose();
  }

  void _onJsonLoaded(FormStructure formStructure) {
    setState(() {
      _currentFormStructure = formStructure;
      _generatedHtml = null;
      _state = GenerationState.idle;
      _errorMessage = null;
    });
  }

  void _onJsonCleared() {
    setState(() {
      _currentFormStructure = null;
      _generatedHtml = null;
      _state = GenerationState.idle;
      _errorMessage = null;
    });
  }

  Future<void> _generateTemplate() async {
    if (_currentFormStructure == null) return;

    setState(() {
      _state = GenerationState.loading;
      _errorMessage = null;
    });

    _buttonAnimationController.forward().then((_) {
      _buttonAnimationController.reverse();
    });

    try {
      final htmlCode =
          await AiService.generateHtmlTemplate(_currentFormStructure!);

      if (mounted) {
        setState(() {
          _generatedHtml = htmlCode;
          _state = GenerationState.success;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _state = GenerationState.error;
          _errorMessage = 'Error al generar la plantilla: \${e.toString()}';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final mediaQuery = MediaQuery.of(context);
    final isDesktop = mediaQuery.size.width >= 1200;

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: CustomScrollView(
        slivers: [
          // App Bar
          SliverAppBar(
            expandedHeight: 120,
            floating: false,
            pinned: true,
            backgroundColor: theme.colorScheme.surface,
            elevation: 0,
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: const EdgeInsets.only(left: 24, bottom: 16),
              title: Text(
                'AI Template Generator',
                style: theme.textTheme.headlineMedium?.copyWith(
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.bold,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                      theme.colorScheme.secondaryContainer
                          .withValues(alpha: 0.2),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Header description
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Genera plantillas de reportes HTML profesionales usando inteligencia artificial',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                      height: 1.5,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.auto_awesome,
                        size: 16,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Potenciado por Firebase Gemini',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Main content
          SliverToBoxAdapter(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isDesktop ? 24 : 16,
                vertical: 24,
              ),
              child: isDesktop
                  ? _buildDesktopLayout(theme)
                  : _buildMobileLayout(theme),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout(ThemeData theme) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left panel - JSON Input
        Expanded(
          flex: 1,
          child: Column(
            children: [
              JsonInputWidget(
                onJsonLoaded: _onJsonLoaded,
                onClear: _onJsonCleared,
              ),
              const SizedBox(height: 24),
              _buildGenerateButton(theme),
            ],
          ),
        ),

        const SizedBox(width: 32),

        // Right panel - HTML Viewer
        Expanded(
          flex: 1,
          child: SizedBox(
            height: 700,
            child: _buildResultSection(theme),
          ),
        ),
      ],
    );
  }

  Widget _buildMobileLayout(ThemeData theme) {
    return Column(
      children: [
        JsonInputWidget(
          onJsonLoaded: _onJsonLoaded,
          onClear: _onJsonCleared,
        ),
        const SizedBox(height: 24),
        _buildGenerateButton(theme),
        const SizedBox(height: 24),
        SizedBox(
          height: 500,
          child: _buildResultSection(theme),
        ),
      ],
    );
  }

  Widget _buildGenerateButton(ThemeData theme) {
    final canGenerate =
        _currentFormStructure != null && _state != GenerationState.loading;

    return AnimatedBuilder(
      animation: _buttonScaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _buttonScaleAnimation.value,
          child: Container(
            width: double.infinity,
            height: 56,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: canGenerate
                  ? LinearGradient(
                      colors: [
                        theme.colorScheme.primary,
                        theme.colorScheme.primary.withValues(alpha: 0.8),
                      ],
                    )
                  : null,
              color: canGenerate ? null : theme.colorScheme.surfaceContainerHighest,
            ),
            child: ElevatedButton.icon(
              onPressed: canGenerate ? _generateTemplate : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              icon: _state == GenerationState.loading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          theme.colorScheme.onPrimary,
                        ),
                      ),
                    )
                  : Icon(
                      Icons.auto_awesome,
                      color: canGenerate
                          ? theme.colorScheme.onPrimary
                          : theme.colorScheme.onSurfaceVariant,
                      size: 24,
                    ),
              label: Text(
                _state == GenerationState.loading
                    ? 'Generando Plantilla...'
                    : 'Generar Plantilla HTML',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: canGenerate
                      ? theme.colorScheme.onPrimary
                      : theme.colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildResultSection(ThemeData theme) {
    switch (_state) {
      case GenerationState.idle:
        return _buildEmptyState(theme);
      case GenerationState.loading:
        return _buildLoadingState(theme);
      case GenerationState.success:
        return HtmlViewerWidget(
          htmlCode: _generatedHtml!,
          title: _currentFormStructure?.title ?? 'plantilla',
        );
      case GenerationState.error:
        return _buildErrorState(theme);
    }
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.html,
              size: 64,
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'Resultado HTML',
              style: theme.textTheme.titleLarge?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Carga un JSON válido y genera tu plantilla',
              style: theme.textTheme.bodyMedium?.copyWith(
                color:
                    theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState(ThemeData theme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 64,
              height: 64,
              child: CircularProgressIndicator(
                strokeWidth: 4,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Generando plantilla HTML...',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'La IA está creando tu plantilla personalizada',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: theme.colorScheme.errorContainer.withValues(alpha: 0.3),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Error en la generación',
                style: theme.textTheme.titleLarge?.copyWith(
                  color: theme.colorScheme.onErrorContainer,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage ?? 'Ocurrió un error desconocido',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onErrorContainer,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _generateTemplate,
                icon: Icon(
                  Icons.refresh,
                  color: theme.colorScheme.onSecondary,
                ),
                label: Text(
                  'Reintentar',
                  style: TextStyle(
                    color: theme.colorScheme.onSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.secondary,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
