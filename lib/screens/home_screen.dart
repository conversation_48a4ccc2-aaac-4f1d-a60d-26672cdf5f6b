import 'dart:io';
import 'package:bitacora_template_generator/services/ai_service.dart';
import 'package:bitacora_template_generator/widgets/html_viewer_widget.dart';
import 'package:bitacora_template_generator/widgets/json_input_widget.dart';
import 'package:bitacora_template_generator/widgets/prompt_input_widget.dart';
import 'package:bitacora_template_generator/widgets/generated_json_display_widget.dart';
import 'package:flutter/material.dart';

enum GenerationState {
  idle,
  promptInput,
  generatingJson,
  jsonGenerated,
  generatingHtml,
  htmlGenerated,
  error,
}

enum WorkflowStep {
  promptInput,
  jsonGeneration,
  jsonDisplay,
  htmlGeneration,
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  String? _currentJsonTemplate;
  String? _generatedHtml;
  GenerationState _state = GenerationState.idle;
  WorkflowStep _currentStep = WorkflowStep.promptInput;
  String? _errorMessage;
  late AnimationController _buttonAnimationController;
  late Animation<double> _buttonScaleAnimation;

  // New workflow state
  bool _useNewWorkflow = true;

  @override
  void initState() {
    super.initState();
    _buttonAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _buttonScaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(
          parent: _buttonAnimationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _buttonAnimationController.dispose();
    super.dispose();
  }

  void _onJsonLoaded(String formStructure) {
    setState(() {
      _currentJsonTemplate = formStructure;
      _generatedHtml = null;
      _state = GenerationState.idle;
      _errorMessage = null;
    });
  }

  void _onJsonCleared() {
    setState(() {
      _currentJsonTemplate = null;
      _generatedHtml = null;
      _state = GenerationState.idle;
      _errorMessage = null;
    });
  }

  // New workflow methods
  Future<void> _onPromptSubmitted(String prompt, File? attachedFile) async {
    setState(() {
      _state = GenerationState.generatingJson;
      _currentStep = WorkflowStep.jsonGeneration;
      _errorMessage = null;
    });

    _buttonAnimationController.forward().then((_) {
      _buttonAnimationController.reverse();
    });

    try {
      final jsonTemplate =
          await AiService.generateJsonFromPrompt(prompt, attachedFile);

      if (mounted) {
        if (jsonTemplate != null && jsonTemplate.isNotEmpty) {
          setState(() {
            _currentJsonTemplate = jsonTemplate;
            _state = GenerationState.jsonGenerated;
            _currentStep = WorkflowStep.jsonDisplay;
            // Stay in new workflow mode - don't switch automatically
          });
        } else {
          setState(() {
            _state = GenerationState.error;
            _errorMessage = 'No se pudo generar el JSON. Intenta con una descripción más específica.';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _state = GenerationState.error;
          _errorMessage = 'Error al generar JSON: ${e.toString()}';
        });
      }
    }
  }

  void _onUseJsonForHtml() {
    if (_currentJsonTemplate != null) {
      setState(() {
        _useNewWorkflow = false; // Switch to JSON → HTML mode
        _currentStep = WorkflowStep.htmlGeneration;
        _state = GenerationState.idle;
        // JSON is preserved in _currentJsonTemplate
      });
    }
  }

  void _onEditJson() {
    setState(() {
      _useNewWorkflow = false;
      _currentStep = WorkflowStep.promptInput;
      _state = GenerationState.idle;
    });
  }

  void _switchToNewWorkflow() {
    setState(() {
      _useNewWorkflow = true;
      _currentStep = WorkflowStep.promptInput;
      _state = GenerationState.idle;
      _currentJsonTemplate = null;
      _generatedHtml = null;
      _errorMessage = null;
    });
  }

  void _switchToLegacyWorkflow() {
    setState(() {
      _useNewWorkflow = false;
      _currentStep = WorkflowStep.promptInput;
      _state = GenerationState.idle;
      // Preserve _currentJsonTemplate if it exists
      _generatedHtml = null;
      _errorMessage = null;
    });
  }

  Future<void> _generateTemplate() async {
    if (_currentJsonTemplate == null) return;

    setState(() {
      _state = GenerationState.generatingHtml;
      _currentStep = WorkflowStep.htmlGeneration;
      _errorMessage = null;
    });

    _buttonAnimationController.forward().then((_) {
      _buttonAnimationController.reverse();
    });

    try {
      final htmlCode =
          await AiService.generateHtmlTemplate(_currentJsonTemplate!);

      if (mounted) {
        setState(() {
          _generatedHtml = htmlCode;
          _state = GenerationState.htmlGenerated;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _state = GenerationState.error;
          _errorMessage = 'Error al generar la plantilla: \${e.toString()}';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final mediaQuery = MediaQuery.of(context);
    final isDesktop = mediaQuery.size.width >= 1200;

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: CustomScrollView(
        slivers: [
          // Header description
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            GestureDetector(
                              onTap: _switchToNewWorkflow,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 4),
                                decoration: BoxDecoration(
                                  color: _useNewWorkflow
                                      ? theme.colorScheme.primary
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Text(
                                  'Prompt → JSON',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: _useNewWorkflow
                                        ? theme.colorScheme.onPrimary
                                        : theme.colorScheme.onSurfaceVariant,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            GestureDetector(
                              onTap: _switchToLegacyWorkflow,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 4),
                                decoration: BoxDecoration(
                                  color: !_useNewWorkflow
                                      ? theme.colorScheme.primary
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Text(
                                  'JSON → HTML',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: !_useNewWorkflow
                                        ? theme.colorScheme.onPrimary
                                        : theme.colorScheme.onSurfaceVariant,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Main content
          SliverToBoxAdapter(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isDesktop ? 24 : 16,
                vertical: 24,
              ),
              child: isDesktop
                  ? (_useNewWorkflow
                      ? _buildNewDesktopLayout(theme)
                      : _buildDesktopLayout(theme))
                  : (_useNewWorkflow
                      ? _buildNewMobileLayout(theme)
                      : _buildMobileLayout(theme)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNewDesktopLayout(ThemeData theme) {
    final screenHeight = MediaQuery.of(context).size.height;
    final availableHeight =
        screenHeight - 280; // Account for app bar and padding

    // For Prompt → JSON step, use full width
    if (_currentStep == WorkflowStep.promptInput ||
        _currentStep == WorkflowStep.jsonDisplay) {
      return Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 800),
          height: availableHeight,
          child: Column(
            children: [
              if (_currentStep == WorkflowStep.promptInput) ...[
                Expanded(
                  child: PromptInputWidget(
                    onPromptSubmitted: _onPromptSubmitted,
                  ),
                ),
              ] else if (_currentStep == WorkflowStep.jsonDisplay &&
                  _currentJsonTemplate != null) ...[
                Expanded(
                  child: GeneratedJsonDisplayWidget(
                    jsonString: _currentJsonTemplate!,
                    onGenerateHtml: _onUseJsonForHtml,
                    onEdit: _onEditJson,
                  ),
                ),
              ],
            ],
          ),
        ),
      );
    }

    // For other steps, use two-panel layout
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left panel - JSON Input
        Expanded(
          flex: 1,
          child: SizedBox(
            height: availableHeight,
            child: Column(
              children: [
                // Show JSON structure at the top when generating HTML
                if (_currentJsonTemplate != null) ...[
                  SizedBox(
                    height: 200,
                    child: GeneratedJsonDisplayWidget(
                      jsonString: _currentJsonTemplate!,
                      onEdit: _onEditJson,
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
                Expanded(
                  child: JsonInputWidget(
                    onJsonLoaded: _onJsonLoaded,
                    onClear: _onJsonCleared,
                  ),
                ),
                const SizedBox(height: 24),
                _buildGenerateButton(theme),
              ],
            ),
          ),
        ),

        const SizedBox(width: 32),

        // Right panel - Result section
        Expanded(
          flex: 1,
          child: SizedBox(
            height: availableHeight,
            child: _buildResultSection(theme),
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(ThemeData theme) {
    final screenHeight = MediaQuery.of(context).size.height;
    final availableHeight =
        screenHeight - 280; // Account for app bar and padding

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left panel - JSON Input
        Expanded(
          flex: 1,
          child: SizedBox(
            height: availableHeight,
            child: Column(
              children: [
                Expanded(
                  child: JsonInputWidget(
                    onJsonLoaded: _onJsonLoaded,
                    onClear: _onJsonCleared,
                  ),
                ),
                const SizedBox(height: 24),
                _buildGenerateButton(theme),
              ],
            ),
          ),
        ),

        const SizedBox(width: 32),

        // Right panel - HTML Viewer
        Expanded(
          flex: 1,
          child: SizedBox(
            height: availableHeight,
            child: _buildResultSection(theme),
          ),
        ),
      ],
    );
  }

  Widget _buildNewMobileLayout(ThemeData theme) {
    final screenHeight = MediaQuery.of(context).size.height;
    final availableHeight =
        screenHeight - 280; // Account for app bar and padding

    // For Prompt → JSON step, use full height
    if (_currentStep == WorkflowStep.promptInput ||
        _currentStep == WorkflowStep.jsonDisplay) {
      return SizedBox(
        height: availableHeight,
        child: Column(
          children: [
            if (_currentStep == WorkflowStep.promptInput) ...[
              Expanded(
                child: PromptInputWidget(
                  onPromptSubmitted: _onPromptSubmitted,
                ),
              ),
            ] else if (_currentStep == WorkflowStep.jsonDisplay &&
                _currentJsonTemplate != null) ...[
              Expanded(
                child: GeneratedJsonDisplayWidget(
                  jsonString: _currentJsonTemplate!,
                  onGenerateHtml: _onUseJsonForHtml,
                  onEdit: _onEditJson,
                ),
              ),
            ],
          ],
        ),
      );
    }

    // For JSON → HTML step
    return SizedBox(
      height: availableHeight,
      child: Column(
        children: [
          // Show JSON structure at the top when generating HTML
          if (_currentJsonTemplate != null) ...[
            SizedBox(
              height: 150,
              child: GeneratedJsonDisplayWidget(
                jsonString: _currentJsonTemplate!,
                onEdit: _onEditJson,
              ),
            ),
            const SizedBox(height: 12),
          ],
          Expanded(
            child: Column(
              children: [
                Expanded(
                  child: JsonInputWidget(
                    onJsonLoaded: _onJsonLoaded,
                    onClear: _onJsonCleared,
                  ),
                ),
                const SizedBox(height: 16),
                _buildGenerateButton(theme),
                const SizedBox(height: 16),
                Expanded(
                  child: _buildResultSection(theme),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileLayout(ThemeData theme) {
    final screenHeight = MediaQuery.of(context).size.height;
    final availableHeight =
        screenHeight - 280; // Account for app bar and padding

    return SizedBox(
      height: availableHeight,
      child: Column(
        children: [
          Expanded(
            flex: 2,
            child: JsonInputWidget(
              onJsonLoaded: _onJsonLoaded,
              onClear: _onJsonCleared,
            ),
          ),
          const SizedBox(height: 24),
          _buildGenerateButton(theme),
          const SizedBox(height: 24),
          Expanded(
            flex: 2,
            child: _buildResultSection(theme),
          ),
        ],
      ),
    );
  }

  Widget _buildGenerateButton(ThemeData theme) {
    final canGenerate = _currentJsonTemplate != null &&
        _state != GenerationState.generatingJson &&
        _state != GenerationState.generatingHtml;

    return AnimatedBuilder(
      animation: _buttonScaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _buttonScaleAnimation.value,
          child: Container(
            width: double.infinity,
            height: 56,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: canGenerate
                  ? LinearGradient(
                      colors: [
                        theme.colorScheme.primary,
                        theme.colorScheme.primary.withValues(alpha: 0.8),
                      ],
                    )
                  : null,
              color: canGenerate
                  ? null
                  : theme.colorScheme.surfaceContainerHighest,
            ),
            child: ElevatedButton.icon(
              onPressed: canGenerate ? _generateTemplate : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              icon: (_state == GenerationState.generatingJson ||
                      _state == GenerationState.generatingHtml)
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          theme.colorScheme.onPrimary,
                        ),
                      ),
                    )
                  : Icon(
                      _state == GenerationState.generatingJson
                          ? Icons.data_object
                          : Icons.html,
                      color: canGenerate
                          ? theme.colorScheme.onPrimary
                          : theme.colorScheme.onSurfaceVariant,
                      size: 24,
                    ),
              label: Text(
                _getButtonText(),
                style: theme.textTheme.titleMedium?.copyWith(
                  color: canGenerate
                      ? theme.colorScheme.onPrimary
                      : theme.colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  String _getButtonText() {
    switch (_state) {
      case GenerationState.generatingJson:
        return 'Generando JSON...';
      case GenerationState.generatingHtml:
        return 'Generando HTML...';
      default:
        return 'Generar Plantilla HTML';
    }
  }

  Widget _buildResultSection(ThemeData theme) {
    // Don't show result section for Prompt → JSON steps
    if (_useNewWorkflow &&
        (_currentStep == WorkflowStep.promptInput ||
            _currentStep == WorkflowStep.jsonDisplay)) {
      return Container(); // Empty container
    }

    switch (_state) {
      case GenerationState.idle:
      case GenerationState.promptInput:
        return _buildEmptyState(theme);
      case GenerationState.generatingJson:
        return _buildLoadingState(theme, 'Generando estructura JSON...',
            'La IA está analizando tu descripción');
      case GenerationState.jsonGenerated:
        return _buildJsonGeneratedState(theme);
      case GenerationState.generatingHtml:
        return _buildLoadingState(theme, 'Generando plantilla HTML...',
            'La IA está creando tu plantilla personalizada');
      case GenerationState.htmlGenerated:
        return HtmlViewerWidget(
          htmlCode: _generatedHtml!,
          title: 'plantilla',
        );
      case GenerationState.error:
        return _buildErrorState(theme);
    }
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.html,
              size: 64,
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'Resultado HTML',
              style: theme.textTheme.titleLarge?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Carga un JSON válido y genera tu plantilla',
              style: theme.textTheme.bodyMedium?.copyWith(
                color:
                    theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJsonGeneratedState(ThemeData theme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: Colors.green.withValues(alpha: 0.1),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle,
              size: 64,
              color: Colors.green,
            ),
            const SizedBox(height: 16),
            Text(
              'JSON Generado Exitosamente',
              style: theme.textTheme.titleLarge?.copyWith(
                color: Colors.green.shade700,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Revisa la estructura generada y procede a crear la plantilla HTML',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.green.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState(ThemeData theme,
      [String? title, String? subtitle]) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 64,
              height: 64,
              child: CircularProgressIndicator(
                strokeWidth: 4,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              title ?? 'Generando plantilla HTML...',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle ?? 'La IA está creando tu plantilla personalizada',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: theme.colorScheme.errorContainer.withValues(alpha: 0.3),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Error en la generación',
                style: theme.textTheme.titleLarge?.copyWith(
                  color: theme.colorScheme.onErrorContainer,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage ?? 'Ocurrió un error desconocido',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onErrorContainer,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _generateTemplate,
                icon: Icon(
                  Icons.refresh,
                  color: theme.colorScheme.onSecondary,
                ),
                label: Text(
                  'Reintentar',
                  style: TextStyle(
                    color: theme.colorScheme.onSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.secondary,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
