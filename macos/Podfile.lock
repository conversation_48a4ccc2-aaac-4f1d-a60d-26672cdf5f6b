PODS:
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - file_picker (0.0.1):
    - FlutterMacOS
  - Firebase/AppCheck (12.0.0):
    - Firebase/CoreOnly
    - FirebaseAppCheck (~> 12.0.0)
  - Firebase/Auth (12.0.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 12.0.0)
  - Firebase/CoreOnly (12.0.0):
    - FirebaseCore (~> 12.0.0)
  - firebase_app_check (0.4.0):
    - Firebase/AppCheck (~> 12.0.0)
    - Firebase/CoreOnly (~> 12.0.0)
    - firebase_core
    - FlutterMacOS
  - firebase_auth (6.0.1):
    - Firebase/Auth (~> 12.0.0)
    - Firebase/CoreOnly (~> 12.0.0)
    - firebase_core
    - FlutterMacOS
  - firebase_core (4.0.0):
    - Firebase/CoreOnly (~> 12.0.0)
    - FlutterMacOS
  - FirebaseAppCheck (12.0.0):
    - AppCheckCore (~> 11.0)
    - FirebaseAppCheckInterop (~> 12.0.0)
    - FirebaseCore (~> 12.0.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
  - FirebaseAppCheckInterop (12.0.0)
  - FirebaseAuth (12.0.0):
    - FirebaseAppCheckInterop (~> 12.0.0)
    - FirebaseAuthInterop (~> 12.0.0)
    - FirebaseCore (~> 12.0.0)
    - FirebaseCoreExtension (~> 12.0.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GTMSessionFetcher/Core (< 6.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (12.0.0)
  - FirebaseCore (12.0.0):
    - FirebaseCoreInternal (~> 12.0.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (12.0.0):
    - FirebaseCore (~> 12.0.0)
  - FirebaseCoreInternal (12.0.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FlutterMacOS (1.0.0)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (5.0.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)

DEPENDENCIES:
  - file_picker (from `Flutter/ephemeral/.symlinks/plugins/file_picker/macos`)
  - firebase_app_check (from `Flutter/ephemeral/.symlinks/plugins/firebase_app_check/macos`)
  - firebase_auth (from `Flutter/ephemeral/.symlinks/plugins/firebase_auth/macos`)
  - firebase_core (from `Flutter/ephemeral/.symlinks/plugins/firebase_core/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)

SPEC REPOS:
  trunk:
    - AppCheckCore
    - Firebase
    - FirebaseAppCheck
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - GoogleUtilities
    - GTMSessionFetcher
    - PromisesObjC

EXTERNAL SOURCES:
  file_picker:
    :path: Flutter/ephemeral/.symlinks/plugins/file_picker/macos
  firebase_app_check:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_app_check/macos
  firebase_auth:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_auth/macos
  firebase_core:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_core/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin

SPEC CHECKSUMS:
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  file_picker: 7584aae6fa07a041af2b36a2655122d42f578c1a
  Firebase: 800d487043c0557d9faed71477a38d9aafb08a41
  firebase_app_check: 6b3c944d2559ce9b5ea7711e5132944dd10e06b6
  firebase_auth: 1d35efb142c2a0ae7e8028e719052c73399ea267
  firebase_core: eeea10f64026b68cd0bc3dee079ab4717e22909e
  FirebaseAppCheck: b91ab9185b0192dabe7efe83d4b66de33f0d83b0
  FirebaseAppCheckInterop: c848d06a04030c9858ef0ae555b82035dbe470d0
  FirebaseAuth: 654e4de84787c45d7265599a651038e854ccb439
  FirebaseAuthInterop: 002da671896af5e8879ae117dc604ed240b86e80
  FirebaseCore: 055f4ab117d5964158c833f3d5e7ec6d91648d4a
  FirebaseCoreExtension: 639afb3de6abd611952be78a794c54a47fa0f361
  FirebaseCoreInternal: dedc28e569a4be85f38f3d6af1070a2e12018d55
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMSessionFetcher: 02d6e866e90bc236f48a703a041dfe43e6221a29
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47

PODFILE CHECKSUM: 54d867c82ac51cbd61b565781b9fada492027009

COCOAPODS: 1.16.2
